import { useState } from "react";
import { Canvas } from "fabric";
import { applyCrop, clearCrop } from "@/lib/fabric/operations";
import { toBaseOrientation } from "@/lib/fabric/operations";
import { CropData, CropManagementState } from "@/shared/types";

/*
Crop management: toggles crop apply/clear, saves a base-orientation normalized
rect for stable reapply after transforms.
*/
export const useCropManagement = (
  fabricCanvas: React.RefObject<Canvas | null>,
  initialCropData: CropData
): CropManagementState => {
  const [cropData, setCropData] = useState<CropData>(initialCropData);

  const handleCrop = () => {
    if (!fabricCanvas?.current) return;

    const canvas = fabricCanvas.current;

    if (cropData.isCropped) {
      clearCrop(canvas);

      setCropData({
        isCropped: false,
        normalizedCropRect: undefined,
      });
      return;
    }

    let cropRect = canvas.getActiveObject();
    if (!(cropRect as any)?.name || (cropRect as any).name !== "cropRect") {
      cropRect = canvas.getObjects().find((obj) => (obj as any).name === "cropRect");
    }

    if (!cropRect) return;

    const left = cropRect.left || 0;
    const top = cropRect.top || 0;
    const width = (cropRect.width || 0) * (cropRect.scaleX || 1);
    const height = (cropRect.height || 0) * (cropRect.scaleY || 1);

    const canvasWidth = canvas.getWidth();
    const canvasHeight = canvas.getHeight();

    // Save crop as normalized rect in base (unrotated, unflipped) orientation for reapply
    const currentRect = {
      left: left / canvasWidth,
      top: top / canvasHeight,
      width: width / canvasWidth,
      height: height / canvasHeight,
    };

    const angle = canvas.backgroundImage?.angle || 0;
    const flipX = !!canvas.backgroundImage?.flipX;
    const flipY = !!canvas.backgroundImage?.flipY;

    const normalizedCropRect = toBaseOrientation(currentRect, angle, flipX, flipY);

    applyCrop(canvas, { left, top, width, height });
    canvas.remove(cropRect);
    canvas.renderAll();

    // Persist globally on canvas for transform reapply
    (canvas as any).__normalizedCropRectBase = normalizedCropRect;

    setCropData({
      isCropped: true,
      normalizedCropRect,
    });
  };

  return {
    cropData,
    setCropData,
    handleCrop,
  };
};
