import { Canvas } from "fabric";
import { isArrow } from "./arrows";
import { isMeasurementLine } from "./measurements";

/*
Finds and updates the arrow head color for a given arrow line.
Looks for the head property first, then searches canvas for arrowHead objects.
*/
const updateArrowHeadColor = (arrowLine: any, newColor: string): void => {
  const head = arrowLine.head;
  if (head) {
    head.set({ fill: newColor });
    return;
  }
};

/*
Finds and updates the measurement text color for a given measurement line.
Looks for the measurementText property first, then searches canvas by position/ID.
*/
const updateMeasurementTextColor = (measurementLine: any, newColor: string): void => {
  const measurementText = measurementLine.measurementText;
  if (measurementText) {
    measurementText.set({ fill: newColor });
    return;
  }
};

/*
Updates the color of a single annotation object based on its type.
Handles different color properties (stroke vs fill) and associated objects:
- Text: uses fill color
- Shapes: uses stroke color
- Highlights: uses both fill (with opacity) and stroke
- Arrows: updates both line stroke and arrowhead fill
- Measurement lines: updates both line stroke and measurement text fill
*/
export const applyColorToSingleObject = (obj: any, newColor: string): void => {
  if (!obj || !newColor) return;

  const objName = obj.name;
  const objType = obj.type;

  switch (objType) {
    case "textbox":
      obj.set({ fill: newColor });
      break;
    case "rect":
    case "circle":
    case "line":
      if (objName === "highlight") {
        const fillColor = newColor + "40";
        obj.set({ fill: fillColor, stroke: newColor });
      } else if (isArrow(obj)) {
        obj.set({ stroke: newColor });
        updateArrowHeadColor(obj, newColor);
      } else if (objName === "measurementLine") {
        obj.set({ stroke: newColor });
        updateMeasurementTextColor(obj, newColor);
      } else {
        obj.set({ stroke: newColor });
      }
      break;

    case "path":
      obj.set({ stroke: newColor });
      break;

    default:
      if (isArrow(obj)) {
        obj.set({ stroke: newColor });
        updateArrowHeadColor(obj, newColor);
      } else if (isMeasurementLine(obj)) {
        obj.set({ stroke: newColor });
        updateMeasurementTextColor(obj, newColor);
      } else {
        obj.set({ stroke: newColor });
      }
      break;
  }

  obj.setCoords();
};

/*
Updates the color of the currently selected object(s) on the canvas.
Handles both single selections and multiple selections (ActiveSelection).
Preserves the selection state after color changes by restoring it after rendering.
*/
export const changeSelectedAnnotationColors = (canvas: Canvas, newColor: string): void => {
  const activeObject = canvas.getActiveObject();

  if (!activeObject) return;

  const selectedObjects =
    activeObject.type === "activeselection" ? (activeObject as any).getObjects() : [activeObject];

  selectedObjects.forEach((obj: any) => {
    applyColorToSingleObject(obj, newColor);
  });

  const active = canvas.getActiveObject();
  if (active) {
    if (active.type === "activeselection") {
      canvas.setActiveObject(active);
    } else {
      canvas.setActiveObject(selectedObjects[0]);
    }
  }
  canvas.requestRenderAll();
};
