import { Canvas } from "fabric";
import { deactivateMagnifierForTool, activateMagnifier } from "@/lib/fabric/operations/magnifier";

/** Sets canvas interaction and object selectability based on tool mode */
export const setToolMode = (toolMode: string, canvas: Canvas) => {
  if (toolMode !== "magnifier") deactivateMagnifierForTool(canvas);

  if (toolMode === "select") {
    canvas.defaultCursor = "default";
    canvas.isDrawingMode = false;
    canvas.selection = true;

    canvas.forEachObject((obj) => {
      const name = (obj as any).name;
      const selectable =
        name !== "backgroundImage" && name !== "measurementText" && name !== "arrowHead";
      obj.selectable = selectable;
      obj.evented = selectable;
      obj.hasControls = selectable;
      obj.hasBorders = selectable;
      obj.setCoords();
    });
  } else if (toolMode === "freehand") {
    canvas.defaultCursor = "crosshair";
    canvas.isDrawingMode = true;
    canvas.selection = false;
    canvas.discardActiveObject();
    canvas.forEachObject((obj) => {
      if ((obj as any).name !== "backgroundImage") {
        obj.selectable = false;
        obj.evented = false;
        obj.hasControls = false;
        obj.hasBorders = false;
        obj.setCoords();
      }
    });
  } else if (toolMode === "magnifier") {
    canvas.defaultCursor = "crosshair";
    canvas.isDrawingMode = false;
    canvas.selection = false;
    canvas.discardActiveObject();
    canvas.forEachObject((obj) => {
      if ((obj as any).name !== "backgroundImage") {
        obj.selectable = false;
        obj.evented = false;
        obj.hasControls = false;
        obj.hasBorders = false;
        obj.setCoords();
      }
    });
    activateMagnifier(canvas);
  } else {
    canvas.defaultCursor = "crosshair";
    canvas.isDrawingMode = false;
    canvas.selection = false;
    canvas.discardActiveObject();
    canvas.forEachObject((obj) => {
      if ((obj as any).name !== "backgroundImage") {
        obj.selectable = false;
        obj.evented = false;
        obj.hasControls = false;
        obj.hasBorders = false;
        obj.setCoords();
      }
    });
  }

  canvas.renderAll();
};
