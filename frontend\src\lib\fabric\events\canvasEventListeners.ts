import { Canvas } from "fabric";
import { updateMeasurementOnModify, isMeasurementLine } from "../operations/measurements";
import { updateArrowOnModify, isArrow } from "../operations/arrows";
import { onProtractorModified, isProtractorPart } from "../operations/protractor";
import {
  FabricMeasurementLine,
  FabricObjectState,
  UndoAction,
  UndoTrackingState,
} from "@/shared/types";
import { v4 as uuidv4 } from "uuid";

/*
Unified annotation update handler that handles all annotation types consistently.
Each annotation type has its own helper function and update method.
*/
const updateAnnotationOnModify = (canvas: Canvas, target: any): void => {
  if (isMeasurementLine(target)) {
    updateMeasurementOnModify(canvas, target as FabricMeasurementLine);
  } else if (isArrow(target)) {
    updateArrowOnModify(canvas, target);
  } else if (isProtractorPart(target)) {
    onProtractorModified(canvas, target);
  }
};

/*
Tracks add actions for undo stack. Skips background/crop/calibration/measurement text/ arrow head.
*/
export const createUndoTrackingAddHandler = (undoTracking: UndoTrackingState, canvas: Canvas) => {
  return (e?: any) => {
    if (!undoTracking.isUndoingRef.current) {
      const addedObject = e?.path || e?.target;
      if (!addedObject) return;

      const objName = (addedObject as any).name;

      if (objName === "measurementLine") {
        undoTracking.addUndoAction({
          type: "add-measurement",
          lineId: addedObject?.id,
        } as UndoAction);
      } else if (objName === "arrow") {
        undoTracking.addUndoAction({ type: "add-arrow", lineId: addedObject?.id } as UndoAction);
      } else if (objName === "protractor") {
        undoTracking.addUndoAction({
          type: "add-protractor",
          lineId: addedObject?.id,
        } as UndoAction);
      } else if (
        !objName ||
        (!objName.includes("background") &&
          objName !== "cropRect" &&
          objName !== "measurementText" &&
          objName !== "calibrateLine" &&
          objName !== "arrowHead" &&
          objName !== "protractorRayB" &&
          objName !== "protractorArc" &&
          objName !== "protractorText")
      ) {
        undoTracking.addUndoAction({
          type: "add",
          objectCount: canvas.getObjects().length,
        } as UndoAction);
      }
    }
  };
};

/*
Tracks remove actions for undo stack (ignores calibration markers).
*/
export const createUndoTrackingRemoveHandler = (undoTracking: UndoTrackingState) => {
  return (e?: any) => {
    if (!undoTracking.isUndoingRef.current) {
      const removedObject = e?.path || e?.target;
      const objName = (removedObject as any).name;
      if (objName !== "calibrateLine") {
        undoTracking.addUndoAction({
          type: "remove",
          objectCount: 0,
        });
      }
    }
  };
};

/*
On modify, records previous object state captured during move/scale/rotate for undo.
Ignores measurement text.
*/
export const createUndoTrackingModifyHandler = (
  undoTracking: UndoTrackingState,
  objectStates: React.MutableRefObject<Map<string, FabricObjectState>>
) => {
  return (e: { target: any }) => {
    if (
      undoTracking.isUndoingRef.current ||
      !e.target ||
      (e.target as any)?.name === "measurementText" ||
      (e.target as any)?.name === "arrow"
    )
      return;

    const obj = e.target as any;
    const objId = obj.id as string;
    const previousState = objectStates.current.get(objId);
    if (previousState) {
      undoTracking.addUndoAction({ type: "modify", objectId: objId, previousState });
      objectStates.current.delete(objId);
    }
  };
};

/*
Before modifications, snapshot an object's basic transform so we can undo the change.
Skips measurement text to avoid interfering with overlays.
*/
export const createObjectStateTrackingHandler = (
  undoTracking: UndoTrackingState,
  objectStates: React.MutableRefObject<Map<string, FabricObjectState>>
) => {
  return (e: { target: any }) => {
    if (
      undoTracking.isUndoingRef.current ||
      !e.target ||
      isMeasurementLine(e.target) ||
      (e.target as any)?.name === "measurementText" ||
      (e.target as any)?.name === "arrowHead"
    )
      return;

    const obj = e.target as any;
    let objId = obj.id;
    if (!objId) {
      objId = uuidv4();
      obj.id = objId;
    }
    if (!objectStates.current.has(objId)) {
      const base: FabricObjectState = {
        left: obj.left || 0,
        top: obj.top || 0,
        scaleX: obj.scaleX || 1,
        scaleY: obj.scaleY || 1,
        angle: obj.angle || 0,
      };
      const name = (obj as any)?.name as string | undefined;
      if (
        name === "protractor" ||
        name === "protractorRayB" ||
        name === "measurementLine" ||
        name === "arrow"
      ) {
        base.x1 = obj.x1;
        base.y1 = obj.y1;
        base.x2 = obj.x2;
        base.y2 = obj.y2;
      }

      objectStates.current.set(objId, base);
    }
  };
};

/*
Unified annotation modification handler that keeps all annotation types in sync
as objects move/scale/rotate.
*/
export const createAnnotationModificationHandler = (
  canvas: Canvas,
  trackModifyHandler: (e: { target: any }) => void
) => {
  return (e: { target: any }) => {
    updateAnnotationOnModify(canvas, e.target);
    trackModifyHandler(e);
  };
};

/*
Unified annotation movement handler for all annotation types.
*/
export const createAnnotationMovementHandler = (canvas: Canvas) => {
  return (e: { target: any }) => {
    updateAnnotationOnModify(canvas, e.target);
  };
};

/*
Registers Fabric event handlers for undo tracking, unified annotation sync
and path creation. Returns disposers from Fabric's on() registration.
*/
export const setupCanvasEventListeners = (
  canvas: Canvas,
  undoTracking: UndoTrackingState,
  objectStates: React.MutableRefObject<Map<string, FabricObjectState>>,
  onPathCreated?: () => void,
  onObjectSelected?: (obj: any) => void
): (() => void)[] => {
  const trackAddHandler = createUndoTrackingAddHandler(undoTracking, canvas);
  const trackRemoveHandler = createUndoTrackingRemoveHandler(undoTracking);
  const trackModifyHandler = createUndoTrackingModifyHandler(undoTracking, objectStates);
  const startAnnotationHandler = createObjectStateTrackingHandler(undoTracking, objectStates);
  const modifyAnnotationHandler = createAnnotationModificationHandler(canvas, trackModifyHandler);
  const moveAnnotationHandler = createAnnotationMovementHandler(canvas);

  const handlePathCreated = (e: any) => {
    const path = e.path;
    if (!path) return;
    path.set({ id: uuidv4(), strokeUniform: true, objectCaching: false, perPixelTargetFind: true });
    trackAddHandler(e);
    if (onPathCreated) onPathCreated();
  };

  const handleObjectSelected = (e: any) => {
    if (onObjectSelected && e.target) {
      const objName = (e.target as any)?.name;
      if (
        objName !== "backgroundImage" &&
        objName !== "measurementText" &&
        objName !== "arrowHead"
      ) {
        onObjectSelected(e.target);
      }
    }
  };

  return [
    canvas.on("path:created", handlePathCreated),
    canvas.on("object:added", (e) => {
      if (e.target && e.target.type !== "path") {
        trackAddHandler(e);
      }
    }),
    canvas.on("object:removed", trackRemoveHandler),
    canvas.on("object:moving", (e) => {
      startAnnotationHandler(e);
      moveAnnotationHandler(e);
    }),
    canvas.on("object:scaling", (e) => {
      startAnnotationHandler(e);
      moveAnnotationHandler(e);
    }),
    canvas.on("object:rotating", (e) => {
      startAnnotationHandler(e);
      moveAnnotationHandler(e);
    }),
    canvas.on("object:modified", modifyAnnotationHandler),
    canvas.on("selection:created", handleObjectSelected),
    canvas.on("selection:updated", handleObjectSelected),
  ];
};
