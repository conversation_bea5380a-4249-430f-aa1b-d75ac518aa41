import { MagnifierState } from "@/shared/types/medicalImageTypes";
import { Canvas, FabricImage, Circle } from "fabric";
const canvasMagnifierStates = new Map<Canvas, MagnifierState>();
const magnifierStateSetters = new Map<Canvas, (value: boolean) => void>();

const defaultMagnifierConfig = {
  scale: 2,
  radius: 100,
};

/** Returns or creates magnifier state for a canvas */
const getMagnifierState = (canvas: Canvas): MagnifierState => {
  let state = canvasMagnifierStates.get(canvas);
  if (!state) {
    state = {
      scale: defaultMagnifierConfig.scale,
      radius: defaultMagnifierConfig.radius,
    };
    canvasMagnifierStates.set(canvas, state);
  }
  return state;
};

/** Creates the magnifier lens and border overlay using the background image source */
const createMagnifierLens = async (canvas: Canvas): Promise<void> => {
  const state = getMagnifierState(canvas);

  if (!canvas.backgroundImage || state.lensImage) {
    return;
  }

  const originalImage = canvas.backgroundImage as FabricImage;
  if (!originalImage) {
    return;
  }

  const imageElement = originalImage.getElement() as HTMLImageElement;
  if (!imageElement || !imageElement.src) {
    return;
  }

  const { FabricImage } = await import("fabric");
  const lens = await FabricImage.fromURL(imageElement.src, {
    crossOrigin: "anonymous",
  });

  const originalScaleX = originalImage.scaleX || 1;
  const originalScaleY = originalImage.scaleY || 1;

  const bgCenter = originalImage.getCenterPoint();

  lens.set({
    left: bgCenter.x,
    top: bgCenter.y,
    scaleX: originalScaleX * state.scale,
    scaleY: originalScaleY * state.scale,
    selectable: false,
    evented: false,
    opacity: 1,
    visible: false,
    name: "magnifierLens",
    originX: "center",
    originY: "center",
  });

  lens.angle = originalImage.angle || 0;
  lens.flipX = !!originalImage.flipX;
  lens.flipY = !!originalImage.flipY;

  const clipCircle = new Circle({
    radius: state.radius,
    left: 0,
    top: 0,
    originX: "center",
    originY: "center",
    fill: "black",
    selectable: false,
    evented: false,
    absolutePositioned: true,
  });

  lens.clipPath = clipCircle;

  const maskCircle = new Circle({
    radius: state.radius,
    left: 0,
    top: 0,
    originX: "center",
    originY: "center",
    fill: "transparent",
    strokeUniform: true,
    visible: false,
    selectable: false,
    evented: false,
    name: "magnifierBorder",
  });

  state.lensImage = lens;
  state.maskCircle = maskCircle;
  state.clipCircle = clipCircle;
  state.originalImage = originalImage;

  canvas.add(lens);
  canvas.add(maskCircle);

  canvas.bringObjectToFront(lens);
  canvas.bringObjectToFront(maskCircle);

  canvas.renderAll();
};

/** Positions the magnifier under the cursor, honoring bg rotation/flip/scale */
const updateMagnifierPosition = (canvas: Canvas, mouseX: number, mouseY: number): void => {
  const state = getMagnifierState(canvas);

  if (!state.lensImage || !state.maskCircle || !state.clipCircle) {
    return;
  }

  state.lastX = mouseX;
  state.lastY = mouseY;

  const bg = state.originalImage;
  const lens = state.lensImage;
  const mask = state.maskCircle;
  const clip = state.clipCircle;
  if (!bg || !lens || !mask || !clip) return;

  const bgCenter = bg.getCenterPoint();
  const dx = mouseX - bgCenter.x;
  const dy = mouseY - bgCenter.y;

  const angle = ((bg.angle || 0) * Math.PI) / 180;
  const cos = Math.cos(-angle);
  const sin = Math.sin(-angle);

  const rx = cos * dx - sin * dy;
  const ry = sin * dx + cos * dy;

  const fx = bg.flipX ? -rx : rx;
  const fy = bg.flipY ? -ry : ry;

  const scaleX = bg.scaleX || 1;
  const scaleY = bg.scaleY || 1;

  const imgX = fx / scaleX;
  const imgY = fy / scaleY;

  const lensScaleX = scaleX * state.scale;
  const lensScaleY = scaleY * state.scale;

  const sx = (bg.flipX ? -1 : 1) * imgX * lensScaleX;
  const sy = (bg.flipY ? -1 : 1) * imgY * lensScaleY;

  const cosa = Math.cos(angle);
  const sina = Math.sin(angle);
  const vxc = cosa * sx - sina * sy;
  const vyc = sina * sx + cosa * sy;

  const lensLeft = mouseX - vxc;
  const lensTop = mouseY - vyc;

  lens.set({
    left: lensLeft,
    top: lensTop,
    angle: bg.angle || 0,
    flipX: !!bg.flipX,
    flipY: !!bg.flipY,
    scaleX: lensScaleX,
    scaleY: lensScaleY,
  });
  mask.set({ left: mouseX, top: mouseY });
  clip.set({ left: mouseX, top: mouseY });

  canvas.requestRenderAll();
};

/** Registers mouse move/enter/leave handlers for the magnifier */
const setupMagnifierEvents = (canvas: Canvas): (() => void) => {
  const state = getMagnifierState(canvas);

  const handleMouseMove = (e: { pointer?: { x: number; y: number } }) => {
    const pointer = e.pointer;
    if (!pointer) return;

    const canvasWidth = canvas.getWidth();
    const canvasHeight = canvas.getHeight();

    if (pointer.x >= 0 && pointer.x <= canvasWidth && pointer.y >= 0 && pointer.y <= canvasHeight) {
      updateMagnifierPosition(canvas, pointer.x, pointer.y);
    }
  };

  const handleMouseOut = () => {
    if (state.lensImage) {
      state.lensImage.set({ visible: false });
    }
    if (state.maskCircle) {
      state.maskCircle.set({ visible: false });
    }
    canvas.renderAll();
  };

  const handleMouseEnter = () => {
    if (state.lensImage) {
      state.lensImage.set({ visible: true });
    }
    if (state.maskCircle) {
      state.maskCircle.set({ visible: true });
    }
    canvas.renderAll();
  };

  canvas.on("mouse:move", handleMouseMove);
  canvas.on("mouse:out", handleMouseOut);
  canvas.on("mouse:over", handleMouseEnter);

  return () => {
    canvas.off("mouse:move", handleMouseMove);
    canvas.off("mouse:out", handleMouseOut);
    canvas.off("mouse:over", handleMouseEnter);
  };
};

/** Activates magnifier: creates lens and registers events; returns disposer */
export const activateMagnifier = (canvas: Canvas): (() => void) => {
  const state = getMagnifierState(canvas);

  createMagnifierLens(canvas);
  const eventCleanup = setupMagnifierEvents(canvas);
  state.eventCleanup = eventCleanup;

  return () => {
    deactivateMagnifier(canvas);
  };
};

/** Deactivates magnifier: removes overlay objects and event listeners */
const deactivateMagnifier = (canvas: Canvas): void => {
  const state = getMagnifierState(canvas);

  if (state.lensImage) {
    canvas.remove(state.lensImage);
    state.lensImage = undefined;
  }

  if (state.maskCircle) {
    canvas.remove(state.maskCircle);
    state.maskCircle = undefined;
  }

  if (state.clipCircle) {
    state.clipCircle = undefined;
  }

  if (state.eventCleanup) {
    state.eventCleanup();
    state.eventCleanup = undefined;
  }

  canvas.renderAll();
};

/** True if magnifier overlay objects exist and are on the canvas */
const isMagnifierActive = (canvas: Canvas): boolean => {
  const state = canvasMagnifierStates.get(canvas);
  if (!state?.lensImage || !state?.maskCircle) return false;

  const canvasObjects = canvas.getObjects();
  return canvasObjects.includes(state.lensImage) && canvasObjects.includes(state.maskCircle);
};

/** Creates a magnifier toggle handler bound to a React ref and setter */
export const createMagnifierHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setMagnifier: (value: boolean) => void
) => {
  return (value: boolean) => {
    const canvas = fabricCanvas.current;
    if (!canvas) return;

    magnifierStateSetters.set(canvas, setMagnifier);

    if (isMagnifierActive(canvas)) {
      deactivateMagnifier(canvas);
    }

    if (value) {
      activateMagnifier(canvas);
    }

    setMagnifier(value);
  };
};

export const syncMagnifierOnCanvasChange = (canvas: Canvas): void => {
  const state = getMagnifierState(canvas);
  if (!state.lensImage || !state.maskCircle || !state.clipCircle) return;
  const x = state.lastX ?? canvas.getWidth() / 2;
  const y = state.lastY ?? canvas.getHeight() / 2;
  updateMagnifierPosition(canvas, x, y);
};

/** Deactivates magnifier for tool switches and updates external state */
export const deactivateMagnifierForTool = (canvas: Canvas): void => {
  if (isMagnifierActive(canvas)) {
    deactivateMagnifier(canvas);

    const stateSetter = magnifierStateSetters.get(canvas);
    if (stateSetter) {
      stateSetter(false);
    }
  }
};
