import { Canvas } from "fabric";
import { SetupCanvasParams } from "@/shared/types";
import { loadAnnotations } from "../operations/annotations";
import { applyCanvasFilters } from "../operations/filters";
import { loadCanvasImage } from "../rendering/image";

import {
  applyCanvasRotation,
  applyCanvasFlipHorizontal,
  applyCanvasFlipVertical,
} from "../operations/transforms";

/** Initializes a Fabric canvas, loads image, applies transforms, filters, and annotations */
export const setupImageCanvas = async ({
  canvasElement,
  imageUrl,
  annotations,
  filters,
  existingCanvas,
  transformState,
}: SetupCanvasParams): Promise<{
  canvas: Canvas;
}> => {
  const finalImageSource = imageUrl;

  const applyTransforms = () => {
    if (transformState?.rotations) {
      for (let i = 0; i < transformState.rotations; i++) {
        applyCanvasRotation(canvas);
      }
    }
    if (transformState?.flipHorizontal) {
      applyCanvasFlipHorizontal(canvas, transformState.rotations);
    }
    if (transformState?.flipVertical) {
      applyCanvasFlipVertical(canvas, transformState.rotations);
    }
  };
  if (existingCanvas) {
    existingCanvas.dispose();
  }

  const canvas = new Canvas(canvasElement, {
    selection: true,
    backgroundColor: "transparent",
  });

  const containerRect = canvasElement.parentElement?.getBoundingClientRect();

  await loadCanvasImage(canvas, finalImageSource, {
    containerRect: containerRect || undefined,
  });
  applyTransforms();

  if (filters) {
    canvas.renderAll();
    applyCanvasFilters(canvas, filters);
  }

  if (annotations) {
    await loadAnnotations(canvas, annotations);
  }

  canvas.selection = false;
  canvas.forEachObject((obj) => {
    const objName = (obj as any as Record<string, any>)?.name;
    if (objName !== "backgroundImage") {
      obj.selectable = false;
      obj.evented = false;
    }
  });

  return {
    canvas,
  };
};
