import {
  <PERSON>a<PERSON><PERSON><PERSON><PERSON><PERSON>,
  FaPen,
  FaFont,
  FaSquare,
  FaMinus,
  FaCircle,
  FaCrop,
  FaRuler,
  FaDraftingCompass,
  FaHighlighter,
  FaLongArrowAltRight,
  FaRedo,
  FaArrowsAltH,
  FaArrowsAltV,
  FaFilter,
  FaExchangeAlt,
  FaSearchPlus,
  FaRulerCombined
} from "react-icons/fa";
import { ToolDefinition } from "@/shared/types";

export const toolDefinitions: ToolDefinition[] = [
  {
    icon: FaMousePointer,
    title: "Select/Move objects",
    mode: "select",
    type: "mode",
  },
  {
    icon: FaPen,
    title: "Draw freehand paths",
    mode: "freehand",
    type: "mode",
  },
  {
    icon: FaFont,
    title: "Add text",
    mode: "text",
    type: "mode",
  },
  {
    icon: FaSquare,
    title: "Add rectangle",
    mode: "rect",
    type: "mode",
  },
  {
    icon: <PERSON>a<PERSON><PERSON><PERSON><PERSON>,
    title: "Highlight",
    mode: "highlight",
    type: "mode",
  },
  {
    icon: FaMinus,
    title: "Add line",
    mode: "line",
    type: "mode",
  },
  {
    icon: FaCircle,
    title: "Add circle",
    mode: "circle",
    type: "mode",
  },
  {
    icon: FaCrop,
    title: "Crop image",
    mode: "crop",
    type: "mode",
  },
  {
    icon: FaRuler,
    title: "Measure distance",
    mode: "measure",
    type: "mode",
  },
  {
    icon: FaDraftingCompass,
    title: "Calibrate",
    mode: "calibrate",
    type: "mode",
  },
  {
    icon: FaLongArrowAltRight,
    title: "Pointer",
    mode: "arrow",
    type: "mode",
  },
  {
    icon: FaSearchPlus,
    title: "Magnifying glass",
    mode: "magnifier",
    type: "mode",
  },
  {
    icon: FaRulerCombined,
    title: "Protractor",
    mode: "protractor",
    type: "mode",
  },
  {
    icon: FaRedo,
    title: "Rotate",
    action: "rotate",
    type: "action",
  },
  {
    icon: FaArrowsAltH,
    title: "Flip horizontal",
    action: "flipHorizontal",
    type: "action",
  },
  {
    icon: FaArrowsAltV,
    title: "Flip vertical",
    action: "flipVertical",
    type: "action",
  },
  {
    icon: FaFilter,
    title: "Toggle grayscale",
    action: "grayscale",
    type: "toggle",
  },
  {
    icon: FaExchangeAlt,
    title: "Toggle invert",
    action: "invert",
    type: "toggle",
  },
];
