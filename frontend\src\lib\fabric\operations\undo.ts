import { Canvas } from "fabric";
import { UndoAction, FabricMeasurementLine } from "@/shared/types";
import { onProtractorModified } from "./protractor";

/*
Creates an undo function that applies the last UndoAction and updates the stack.
Handles add, add-measurement (line+text), add-measurement (arrow+arrowHead), modify, and bypasses crop.
*/
export const createUndoHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  undoStack: UndoAction[],
  setUndoStack: React.Dispatch<React.SetStateAction<UndoAction[]>>,
  initialObjectCount: React.MutableRefObject<number>,
  isUndoing: React.MutableRefObject<boolean>
) => {
  return async () => {
    if (!fabricCanvas?.current || !undoStack.length) return;

    const canvas = fabricCanvas.current;
    const action = undoStack[undoStack.length - 1];

    if (action.type === "crop") {
      setUndoStack((prev) => prev.slice(0, -1));
      return;
    }

    isUndoing.current = true;

    if (action.type === "add") {
      const objects = canvas.getObjects();
      if (objects.length > initialObjectCount.current) {
        canvas.remove(objects[objects.length - 1]);
        canvas.renderAll();
      }
    } else if (action.type === "add-measurement") {
      const measurementLines = canvas
        .getObjects()
        .filter((obj) => (obj as any).name === "measurementLine") as FabricMeasurementLine[];
      const lineObj = measurementLines[measurementLines.length - 1];

      if (lineObj) {
        canvas.remove(lineObj);
        if (lineObj.measurementText) {
          canvas.remove(lineObj.measurementText);
        }
      }
      canvas.renderAll();
    } else if (action.type === "add-arrow") {
      const arrows = canvas
        .getObjects()
        .filter((obj) => (obj as any).name === "arrow");
      const arrowLine = arrows[arrows.length - 1];

        if (arrowLine) {
          canvas.remove(arrowLine);
          const head = (arrowLine as any).head;
          if (head && head.canvas) {
            canvas.remove(head);
          }
        }

      canvas.renderAll();
    } else if (action.type === "add-protractor") {
  const owners = canvas.getObjects().filter((o) => (o as any).name === "protractor");
  const owner: any = owners.find((o) => (o as any)?.id === action.lineId) || owners[owners.length - 1];
  if (owner) {
    const rayB = canvas.getObjects().find((o) => (o as any).id === owner.rayBId);
    const arc  = canvas.getObjects().find((o) => (o as any).id === owner.arcId);
    const text = canvas.getObjects().find((o) => (o as any).id === owner.textId);

    if (rayB) canvas.remove(rayB as any);
    if (arc)  canvas.remove(arc as any);
    if (text) canvas.remove(text as any);
    canvas.remove(owner as any);
  }
  canvas.renderAll();

} else if (action.type === "modify") {
  const obj = canvas.getObjects().find((o) => (o as any as { id: string }).id === action.objectId) as any;
  if (obj && action.previousState) {
    obj.set({
      left: action.previousState.left,
      top: action.previousState.top,
      scaleX: action.previousState.scaleX,
      scaleY: action.previousState.scaleY,
      angle: action.previousState.angle,
    });

    const name = (obj as any)?.name as string | undefined;
    if (name === "protractor" || name === "protractorRayB" || name === "measurementLine" || name === "arrow") {
      if (typeof action.previousState.x1 === "number") obj.set({ x1: action.previousState.x1 });
      if (typeof action.previousState.y1 === "number") obj.set({ y1: action.previousState.y1 });
      if (typeof action.previousState.x2 === "number") obj.set({ x2: action.previousState.x2 });
      if (typeof action.previousState.y2 === "number") obj.set({ y2: action.previousState.y2 });
    }

    obj.setCoords();

    if (name === "protractor" || name === "protractorRayB") {
      onProtractorModified(canvas, obj);
    }

    canvas.renderAll();
  }
}
    setUndoStack((prev) => prev.slice(0, -1));
    isUndoing.current = false;
  };
};
