import { <PERSON><PERSON>, Line, Path, FabricText } from "fabric";
import { v4 as uuidv4 } from "uuid";

export type FabricProtractorOwner = Line & {
  id: string;
  name: "protractor";
  phase: 1 | 2;
  vertex: { x: number; y: number };

  // sibling ids
  rayBId?: string;
  arcId?: string;
  textId?: string;

  arcRadius?: number;
  __selfAdded?: boolean;
};

type AnyProtractorPart = (Line | Path | FabricText) & {
  id: string;
  parentId?: string;
  name?: string;
};

const toAngle = (dx: number, dy: number) => Math.atan2(dy, dx); // [-π, π]
const norm = (a: number) => ((a % (2 * Math.PI)) + 2 * Math.PI) % (2 * Math.PI);
const dist = (x1: number, y1: number, x2: number, y2: number) =>
  Math.hypot(x2 - x1, y2 - y1);
const arcPath = (
  cx: number,
  cy: number,
  r: number,
  a1: number,
  a2: number,
  sweep: 0 | 1
) => {
  const largeArc = 0;
  const x1 = cx + r * Math.cos(a1);
  const y1 = cy + r * Math.sin(a1);
  const x2 = cx + r * Math.cos(a2);
  const y2 = cy + r * Math.sin(a2);
  return `M ${x1} ${y1} A ${r} ${r} 0 ${largeArc} ${sweep} ${x2} ${y2}`;
};
const labelPos = (cx: number, cy: number, r: number, aMid: number) => ({
  x: cx + (r + 18) * Math.cos(aMid),
  y: cy + (r + 18) * Math.sin(aMid),
});

const getById = (canvas: Canvas, id?: string) =>
  canvas.getObjects().find((o) => (o as any).id === id) as
    | AnyProtractorPart
    | undefined;

const getOwnerFromAny = (
  canvas: Canvas,
  obj: AnyProtractorPart | null | undefined
): FabricProtractorOwner | undefined => {
  if (!obj) return;
  const name = (obj as any).name as string | undefined;
  if (name === "protractor") return obj as FabricProtractorOwner;
  const parentId = (obj as any).parentId as string | undefined;
  if (!parentId) return;
  const owner = getById(canvas, parentId);
  return owner?.name === "protractor"
    ? (owner as FabricProtractorOwner)
    : undefined;
};

// ---------- creation ----------
export const createProtractor = (
  canvas: Canvas,
  start: { x: number; y: number },
  config: any
): FabricProtractorOwner => {
  const id = uuidv4();
  const stroke = config.stroke || "red";
  const strokeWidth = config.strokeWidth ?? 2;

  // owner rayA (selected during draw)
  const rayA = new Line([start.x, start.y, start.x, start.y], {
    stroke,
    strokeWidth,
    strokeUniform: true,
    selectable: true,
    evented: true,
    hasControls: false,
    hasBorders: false,
    perPixelTargetFind: true,
    hoverCursor: "move",
    name: "protractor",
    objectCaching: false,
  }) as FabricProtractorOwner;
  rayA.id = id;
  rayA.vertex = { x: start.x, y: start.y };
  rayA.phase = 1;
  rayA.arcRadius = 32;

  // second ray (collapsed initially)
  const rayB = new Line([start.x, start.y, start.x, start.y], {
    stroke,
    strokeWidth,
    strokeUniform: true,
    selectable: true,
    evented: true,
    hasControls: false,
    hasBorders: false,
    perPixelTargetFind: true,
    hoverCursor: "move",
    name: "protractorRayB",
    objectCaching: false,
  }) as AnyProtractorPart;
  rayB.id = uuidv4();
  rayB.parentId = id;

  // arc path (hidden until both rays have length)
  const arc = new Path(arcPath(start.x, start.y, 1, 0, 0, 1), {
    stroke,
    strokeWidth,
    strokeUniform: true,
    fill: "transparent",
    selectable: false,
    evented: false,
    name: "protractorArc",
    objectCaching: false,
    visible: false,
  }) as AnyProtractorPart;
  (arc as any).id = uuidv4();
  arc.parentId = id;

  // angle label
  const text = new FabricText("0°", {
    fontSize: 18,
    fill: stroke,
    backgroundColor: "rgba(255,255,255,0.8)",
    selectable: false,
    evented: false,
    originX: "center",
    originY: "center",
    name: "protractorText",
    objectCaching: false,
    visible: false,
  }) as AnyProtractorPart;
  (text as any).id = uuidv4();
  text.parentId = id;

  // add in z-order: arc -> rays -> text
  canvas.add(arc);
  canvas.add(rayA);
  canvas.add(rayB);
  canvas.add(text);

  // link ids on owner
  rayA.rayBId = rayB.id;
  rayA.arcId = (arc as any).id;
  rayA.textId = (text as any).id;

  rayA.__selfAdded = true;

  return rayA;
};

// ---------- live update while dragging ----------
export const updateProtractorSize = (
  owner: FabricProtractorOwner,
  _startPoint: { x: number; y: number },
  currentPoint: { x: number; y: number },
  canvas: Canvas
) => {
  const v = owner.vertex;
  const rayB = getById(canvas, owner.rayBId) as Line | undefined;
  const arc = getById(canvas, owner.arcId) as Path | undefined;
  const text = getById(canvas, owner.textId) as FabricText | undefined;

  if (!rayB || !arc || !text) return;

  if (owner.phase === 1) {
    owner.set({ x1: v.x, y1: v.y, x2: currentPoint.x, y2: currentPoint.y });
    owner.setCoords();
  } else {
    rayB.set({ x1: v.x, y1: v.y, x2: currentPoint.x, y2: currentPoint.y });
    rayB.setCoords();
  }

  const readyA = dist(owner.x1!, owner.y1!, owner.x2!, owner.y2!) > 0.5;
  const readyB = dist(rayB.x1!, rayB.y1!, rayB.x2!, rayB.y2!) > 0.5;

  if (readyA && readyB) {
    const a1 = toAngle(owner.x2! - v.x, owner.y2! - v.y);
    const a2 = toAngle(rayB.x2! - v.x, rayB.y2! - v.y);

    let start = norm(a1);
    let end = norm(a2);
    const diff = end - start;
    if (diff > Math.PI) start += 2 * Math.PI;
    if (diff < -Math.PI) end += 2 * Math.PI;

    const ang = Math.abs(end - start);
    const sweep: 0 | 1 = end - start >= 0 ? 1 : 0;

    // draw interior arc with correct curvature
    const r = owner.arcRadius ?? 32;
    arc.set({
      path: new Path(arcPath(v.x, v.y, r, start, end, sweep)).path,
      visible: true,
    });

    const mid = (start + end) / 2;
    const lp = labelPos(v.x, v.y, r, mid);
    text.set({
      text: `${(ang * (180 / Math.PI)).toFixed(1)}°`,
      left: lp.x,
      top: lp.y,
      visible: true,
    });
  } else {
    arc.set({ visible: false });
    text.set({ visible: false });
  }

  canvas.requestRenderAll();
};

export const advanceProtractorPhase = (
  owner: FabricProtractorOwner
): boolean => {
  if (owner.phase === 1) {
    owner.phase = 2;
    return false;
  }
  return true;
};

export const onProtractorModified = (
  canvas: Canvas,
  target: AnyProtractorPart
) => {
  const owner = getOwnerFromAny(canvas, target);
  if (!owner) return;

  const rayB = getById(canvas, owner.rayBId) as Line | undefined;
  if (!rayB) return;

  updateProtractorSize(
    owner,
    owner.vertex,
    { x: rayB.x2!, y: rayB.y2! },
    canvas
  );
};

export const isProtractorOwner = (obj: any): obj is FabricProtractorOwner =>
  !!obj && (obj as any).name === "protractor";
export const isProtractorPart = (obj: any) =>
  !!obj &&
  typeof (obj as any).name === "string" &&
  (obj as any).name.startsWith("protractor");
