# 🏥 CSOI Fabric Viewer - Developer Operations Guide

> **A React + Fabric.js medical image viewer with advanced annotation and manipulation capabilities**

[![React](https://img.shields.io/badge/React-18+-61DAFB?style=flat-square&logo=react)](https://reactjs.org/)
[![Fabric.js](https://img.shields.io/badge/Fabric.js-Canvas-FF6B6B?style=flat-square)](http://fabricjs.com/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5+-3178C6?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
[![Vite](https://img.shields.io/badge/Vite-5+-646CFF?style=flat-square&logo=vite)](https://vitejs.dev/)

---

## 🚀 Quick Start

### Development Setup

```bash
# Frontend
cd frontend
npm install
npm run dev

# Backend
cd backend
npm install
npm start
```

### Architecture Overview

```mermaid
graph TB
    A[React App] --> B[useFabricViewer Hook]
    B --> C[Fabric.js Canvas]
    C --> D[Image Operations]
    C --> E[Annotation Tools]
    C --> F[Transform Operations]
    D --> G[Filters & Effects]
    E --> H[Measurements & Shapes]
    F --> I[Rotate/Flip/Crop]
```

---

## 📋 Core Operations Reference

### 🔧 Canvas Operations

| Operation     | Purpose                      | Key Files                       | Status  |
| ------------- | ---------------------------- | ------------------------------- | ------- |
| **Setup**     | Initialize canvas with image | `lib/fabric/canvas/setup.ts`    | ✅ Core |
| **Resize**    | Responsive canvas scaling    | `hooks/useResponsiveCanvas.ts`  | ✅ Core |
| **Save/Load** | Persist viewer state         | `lib/fabric/operations/save.ts` | ✅ Core |

### 🎨 Image Manipulation

#### Crop Operations

**🔍 Implementation Concept:**
Crop is a **lossless, visual-only operation** that uses Fabric.js `clipPath` and `viewportTransform` to show only a selected region. The original image and all annotations remain intact - only the visible area changes.

**🧠 Core Logic:**

1. **Normalized Storage**: Crop rectangles are stored in normalized coordinates (0-1 range) relative to the original image dimensions
2. **Transform Resilience**: Base normalized rect is remapped to current orientation (rotation + flips) before reapplying
3. **Visual Implementation**: Uses `clipPath` (defines visible area) + `viewportTransform` (centers and fits crop within canvas)
4. **Annotation Handling**: Objects remain in original coordinates; only viewport changes what's visible

**⚙️ Technical Flow:**

```
User draws crop rect → Convert to normalized coords → Store as base rect
↓
Apply clipPath + viewportTransform → Visual crop appears
↓
On rotate/flip → Remap base rect to new orientation → Reapply crop
```

**📁 Files:** `lib/fabric/operations/crop.ts`, `hooks/useCropManagement.ts`

```typescript
import { applyCrop, clearCrop, applyCropFromNormalized } from "@/lib/fabric/operations/crop";

// Apply crop - returns normalized rect for storage
const { normalizedCropRect } = applyCrop(canvas, { left, top, width, height });
(canvas as any).__normalizedCropRectBase = normalizedCropRect;

// Clear crop - removes clipPath and resets viewport
clearCrop(canvas);

// Reapply after transforms - remaps base rect to current orientation
applyCropFromNormalized(canvas, (canvas as any).__normalizedCropRectBase);
```

**🎯 Key Properties:**

- **Lossless**: Original image data never modified
- **Transform-aware**: Survives rotation/flip operations
- **Selection handling**: Clears selection on crop entry, restores in Select mode
- **Undo behavior**: Managed as toggle, not tracked in undo stack

#### Transform Operations

**🔍 Implementation Concept:**
Transforms modify the **spatial relationship** between canvas dimensions, background image, and all annotations while preserving visual coherence. Each operation recalculates object positions to maintain their relative placement.

**🧠 Rotation Logic (90° Clockwise):**

1. **Canvas Dimensions**: Swap width ↔ height
2. **Background**: Increment angle by 90°, recenter at new canvas center
3. **Object Repositioning**: For each object at (x,y), calculate:
   - `oldCenter = (W/2, H/2)`, `newCenter = (H/2, W/2)`
   - `delta = (x - oldCx, y - oldCy)`
   - `newPos = (newCx - delta.y, newCy + delta.x)`
4. **Angle Update**: Increment each object's angle by 90°

**🧠 Flip Logic (Horizontal/Vertical):**

1. **Axis Determination**: Rotation affects which axis is "horizontal"
   - At 0°/180°: H=horizontal, V=vertical
   - At 90°/270°: H=vertical, V=horizontal (swapped)
2. **Background Flip**: Set `flipX`/`flipY` on background image
3. **Object Mirroring**: Mirror positions around canvas center
4. **Text Preservation**: Keep original flip flags on text objects to prevent mirroring

**⚙️ Transform Sequence:**

```
User triggers transform → Update canvas dimensions (rotation only)
↓
Transform background image (angle/flip)
↓
Recalculate all object positions → Update object angles/flips
↓
Reapply crop if active → Re-sync magnifier if active
```

**📁 Files:** `lib/fabric/operations/transforms.ts`

```typescript
import {
  applyCanvasRotation,
  applyCanvasFlipHorizontal,
  applyCanvasFlipVertical,
} from "@/lib/fabric/operations/transforms";

// Rotate 90° clockwise - swaps canvas W/H, repositions all objects
applyCanvasRotation(canvas);

// Flip operations - mirrors around center, preserves text readability
applyCanvasFlipHorizontal(canvas);
applyCanvasFlipVertical(canvas);
```

**🎯 Key Properties:**

- **Layout Preservation**: Objects maintain relative positions to image content
- **Text Readability**: Flip operations don't mirror text glyphs
- **Crop Compatibility**: Reapplies active crop after transform
- **Undo Tracking**: Each transform is tracked as "modify" action

#### Filters & Effects

**🔍 Implementation Concept:**
Filters are **non-destructive image processing operations** applied to the background image using Fabric.js filter pipeline. They modify pixel values in real-time without altering the original image data.

**🧠 Core Logic:**

1. **Filter Pipeline**: Each filter is a Fabric.js filter object added to `FabricImage.filters` array
2. **Render Caching**: Filters are cached per render cycle for performance
3. **State Management**: Filter parameters stored separately from canvas for serialization
4. **Real-time Application**: Changes immediately trigger `requestRenderAll()`

**⚙️ Filter Types & Implementation:**

- **Brightness/Contrast**: Linear pixel value adjustments
- **Gamma Correction**: Separate R/G/B channel gamma curves for color balance
- **Sharpness**: Convolution kernel for edge enhancement
- **Grayscale**: Luminance-based desaturation
- **Invert**: Pixel value negation (255 - value)

**🔄 Application Flow:**

```
Filter state change → Clear existing filters → Build new filter array
↓
Apply to background image → Cache filter results → Trigger render
↓
Filters persist through save/load → Annotations unaffected
```

**📁 Files:** `lib/fabric/operations/filters.ts`, `hooks/useFilterManagement.ts`

```typescript
import { applyCanvasFilters } from "@/lib/fabric/operations/filters";

// Apply multiple filters simultaneously - all are non-destructive
applyCanvasFilters(canvas, {
  brightness: 1.1, // 0.1 - 2.0 (1.0 = no change)
  contrast: 1.05, // 0.1 - 2.0 (1.0 = no change)
  sharpness: 1, // 0.1 - 2.0 (1.0 = no change)
  gammaR: 1, // 0.1 - 3.0 (1.0 = no change)
  gammaG: 1, // 0.1 - 3.0 (1.0 = no change)
  gammaB: 1, // 0.1 - 3.0 (1.0 = no change)
  grayscale: false, // boolean toggle
  invert: false, // boolean toggle
});
```

**🎯 Key Properties:**

- **Non-destructive**: Original image data preserved
- **Serializable**: Filter state saved with annotations
- **Performance**: Cached rendering prevents recomputation
- **Annotation-safe**: Only affects background image, not objects

### ✏️ Annotation Tools

#### Shape Creation

**🔍 Implementation Concept:**
Shapes are **Fabric.js objects** with standardized properties for consistent behavior across selection modes, transforms, and rendering. Each shape type has specific creation logic while sharing common annotation standards.

**🧠 Core Logic:**

1. **Standard Properties**: All shapes get `strokeUniform: true`, `perPixelTargetFind: true`
2. **Selection Control**: `selectable: false`, `evented: false` by default (enabled in Select mode)
3. **Drawing Flow**: `start → update → finish` pattern for interactive creation
4. **Type-specific Defaults**: Each shape type has predefined stroke, fill, and size settings

**⚙️ Shape Types & Behavior:**

- **Rectangle**: Fixed aspect ratio during creation, uniform stroke scaling
- **Circle**: Radius-based sizing, center-point origin
- **Line**: Two-point definition, arrow-head capable
- **Text**: Editable content, flip-aware positioning, readable through transforms
- **Highlight**: Semi-transparent fill + stroke for emphasis

**🔄 Creation Flow:**

```
User selects tool → Click to start → Drag to size → Release to finish
↓
createInitialShape() → Apply tool defaults → Add to canvas
↓
Set selection policy → Track for undo → Apply current color/style
```

**📁 Files:** `lib/fabric/tools/shapeCreators.ts`, `lib/fabric/tools/toolConfigs.ts`

```typescript
import { createInitialShape } from "@/lib/fabric/tools/shapeCreators";

// Create shape with tool-specific defaults and standard properties
const shape = createInitialShape("rect", pointer, canvas);
canvas.add(shape);

// Available shape types with their characteristics:
// "rect" - Rectangle with uniform stroke scaling
// "circle" - Circle with radius-based sizing
// "line" - Two-point line, can become arrow
// "text" - Editable text with transform awareness
// "highlight" - Semi-transparent overlay rectangle
```

**🎯 Key Properties:**

- **Uniform Scaling**: `strokeUniform: true` keeps stroke width consistent on zoom
- **Pixel Selection**: `perPixelTargetFind: true` enables click-to-select on shape pixels
- **Mode Awareness**: Selection behavior changes based on active tool mode
- **Transform Resilience**: Maintains visual integrity through rotations/flips

#### Arrow Annotations

**🔍 Implementation Concept:**
Arrows are **composite objects** consisting of a selectable line shaft and a non-selectable triangle head. The head automatically positions and orients itself based on the shaft's end point and direction.

**🧠 Core Logic:**

1. **Dual Component**: Line object (selectable) + Triangle head (non-selectable)
2. **Auto-positioning**: Head calculates position/rotation from shaft's x2,y2 coordinates
3. **Synchronized Updates**: Head moves/rotates when shaft is modified
4. **Size Management**: Head size scales proportionally with stroke width

**⚙️ Arrow Mechanics:**

- **Head Creation**: `ensureHead()` creates triangle if not exists
- **Position Calculation**: Head positioned at shaft endpoint with proper rotation
- **Size Formula**: `headSize = strokeWidth * sizeMultiplier`
- **Rotation Logic**: Head angle = shaft angle + 90° (perpendicular to shaft)

**🔄 Update Lifecycle:**

```
User drags arrow → Update shaft coordinates → Calculate head position
↓
ensureHead() → Position triangle at x2,y2 → Rotate to match shaft angle
↓
updateArrowOnModify() → Keep head synchronized → Maintain visual coherence
```

**📁 Files:** `lib/fabric/operations/arrows.ts`

```typescript
import { createArrow, updateArrowSize } from "@/lib/fabric/operations/arrows";

// Create arrow with shaft and auto-positioned head
const start = { x: 10, y: 10 };
const arrow = createArrow(start, { stroke: "#00ff00", strokeWidth: 2 });

// Update during drag or modification - head follows automatically
updateArrowSize(arrow as any, start, { x: 100, y: 100 });

// Arrow head properties are managed internally:
// - Non-selectable (only shaft can be selected)
// - Auto-positioned at shaft endpoint
// - Size scales with strokeWidth
// - Rotation matches shaft direction
```

**🎯 Key Properties:**

- **Composite Design**: Line + Triangle working as single logical unit
- **Selection Model**: Only shaft is selectable, head follows passively
- **Auto-synchronization**: Head updates automatically on shaft changes
- **Undo Tracking**: Both creation and modifications are tracked

#### Color Management

**🔍 Implementation Concept:**
Color changes are **type-aware operations** that apply consistent color updates across different annotation types while preserving selection state and handling composite objects correctly.

**🧠 Core Logic:**

1. **Type Detection**: Identifies object type (text, shape, arrow, measurement) for appropriate color application
2. **Property Mapping**: Different objects use different color properties (fill, stroke, both)
3. **Composite Handling**: For arrows, updates both shaft and head; for measurements, updates line and text
4. **Selection Preservation**: Maintains active selection without timeout hacks

**⚙️ Color Application Rules:**

- **Text Objects**: Update `fill` property for text color
- **Shape Objects**: Update both `fill` and `stroke` properties
- **Arrow Components**: Update shaft stroke and head fill/stroke
- **Measurement Lines**: Update line stroke and text fill

**🔄 Application Flow:**

```
User selects color → Detect selected objects → Identify object types
↓
Apply type-specific color properties → Update composite components
↓
Preserve active selection → Trigger render → Track for undo
```

**📁 Files:** `lib/fabric/operations/colorChange.ts`

```typescript
import { changeSelectedAnnotationColors } from "@/lib/fabric/operations/colorChange";

// Apply color to all selected annotations - handles different object types
changeSelectedAnnotationColors(canvas, "#ff0000");

// Color application behavior by object type:
// - Text: Updates fill (text color)
// - Shapes: Updates both fill and stroke
// - Arrows: Updates shaft stroke + head fill/stroke
// - Measurements: Updates line stroke + text fill
// - Highlights: Updates fill (semi-transparent overlay)
```

**🎯 Key Properties:**

- **Type-aware**: Different color properties for different object types
- **Composite-safe**: Handles multi-part objects (arrows, measurements)
- **Selection-preserving**: No selection clearing or timeout delays
- **Undo-tracked**: Color changes are recorded for undo/redo

#### Measurement Tools

**🔍 Implementation Concept:**
Measurements are **composite objects** combining a selectable line with a non-selectable text label that displays real-world distance using calibration data. The text automatically updates and repositions when the line is modified.

**🧠 Core Logic:**

1. **Dual Component**: Line object (selectable) + Text label (non-selectable, name: "measurementText")
2. **Calibration Awareness**: Converts pixel distance to real units (mm) using calibration factor
3. **Auto-positioning**: Text centers itself on the line and rotates to match line angle
4. **Dynamic Updates**: Text recalculates distance and repositions on line modifications

**⚙️ Measurement Mechanics:**

- **Distance Calculation**: `pixelDistance = √((x2-x1)² + (y2-y1)²)`
- **Real Units**: `realDistance = pixelDistance * calibrationFactor`
- **Text Positioning**: Center point of line with rotation matching line angle
- **Label Format**: "X.X mm" with appropriate decimal precision

**🔄 Update Lifecycle:**

```
User drags measurement → Line coordinates change → Recalculate distance
↓
updateMeasurementText() → Convert to real units → Center text on line
↓
updateMeasurementOnModify() → Maintain text alignment → Preserve readability
```

**📁 Files:** `lib/fabric/operations/measurements.ts`

```typescript
import { createMeasurementLine, updateMeasurementText } from "@/lib/fabric/operations/measurements";

// Create measurement line with auto-generated text label
const start = { x: 20, y: 20 };
const line = createMeasurementLine(start, { stroke: "#ffffff", strokeWidth: 2 });
canvas.add(line);

// Update text with calibrated distance - happens automatically on line changes
updateMeasurementText(canvas, line, calibrationData);

// Measurement behavior:
// - Line is selectable and draggable
// - Text is non-selectable but follows line
// - Distance updates in real-time during modifications
// - Text rotates to match line orientation
// - Calibration converts pixels to real units (mm)
```

**🎯 Key Properties:**

- **Calibration-based**: Uses real-world measurement units, not just pixels
- **Auto-updating**: Distance recalculates automatically on line changes
- **Text Alignment**: Label stays centered and oriented with line
- **Transform Resilience**: Maintains accuracy through rotations/flips

### 🔍 Advanced Features

#### Magnifier Tool

**🔍 Implementation Concept:**
The magnifier is a **pixel-accurate zoom lens** that shows a magnified view of the exact pixel under the cursor, accounting for all image transforms (rotation, flip, scale). It uses coordinate transformation math to map cursor position to original image pixels.

**🧠 Core Logic:**

1. **Lens Creation**: Clones the background image as a separate FabricImage with circular clipPath
2. **Coordinate Mapping**: Transforms cursor coordinates through inverse rotation/flip/scale to find source pixel
3. **Magnification**: Lens scale = background scale × magnifier factor
4. **Positioning**: Positions lens so the cursor pixel appears centered in the magnified view

**⚙️ Coordinate Transformation Math:**

```javascript
// Map cursor to original image coordinates
const delta = (mouseX - bgCenterX, mouseY - bgCenterY);
const rotated = inverseRotate(delta, -backgroundAngle);
const flipped = (flipX ? -rotated.x : rotated.x, flipY ? -rotated.y : rotated.y);
const imageCoords = (flipped.x / bgScaleX, flipped.y / bgScaleY);
```

**🔄 Magnifier Lifecycle:**

```
Mouse move → Calculate image coordinates → Update lens position
↓
Apply transforms to lens → Match background angle/flip → Center on cursor pixel
↓
On resize/rotate/flip → Re-sync lens using last cursor position
```

**📁 Files:** `lib/fabric/operations/magnifier.ts`

```typescript
import { activateMagnifier } from "@/lib/fabric/operations/magnifier";

// Activate magnifier with automatic coordinate transformation
const dispose = activateMagnifier(canvas);

// Magnifier behavior:
// - Follows mouse cursor automatically
// - Shows exact pixel under cursor (transform-aware)
// - Lens scale = background scale × magnification factor
// - Hidden on mouse leave, shown on mouse enter
// - Re-syncs on canvas transforms (rotate/flip/resize)

// Deactivate when done
dispose();
```

**🎯 Key Properties:**

- **Pixel-accurate**: Shows exact source pixel regardless of transforms
- **Transform-aware**: Handles rotation, flip, and scaling correctly
- **Performance-optimized**: Excluded from bulk object scaling operations
- **Ephemeral**: Not tracked in undo system (temporary overlay)

#### Undo/Redo System

**🔍 Implementation Concept:**
The undo system is a **state-based history tracker** that captures object states before modifications and provides reliable rollback functionality. It uses action types and object snapshots to recreate previous canvas states.

**🧠 Core Logic:**

1. **Action Types**: Tracks `add`, `remove`, `modify`, `add-measurement`, `add-arrow`, `add-protractor`
2. **State Snapshots**: Captures object state before modifications using `objectStates` map
3. **Guard Mechanism**: `isUndoingRef` prevents recursive undo operations during rollback
4. **Selective Tracking**: Crop operations bypass undo (managed as toggle)

**⚙️ Undo Action Structure:**

```typescript
interface UndoAction {
  type: "add" | "remove" | "modify" | "add-measurement" | "add-arrow" | "add-protractor";
  objectId?: string; // For single object operations
  objectIds?: string[]; // For multi-object operations
  objectState?: any; // Pre-modification state snapshot
  objectStates?: Record<string, any>; // Multiple object states
}
```

**🔄 Tracking Lifecycle:**

```
Object modification starts → Capture current state → Store in objectStates map
↓
Modification completes → Create UndoAction → Add to undo stack
↓
Clear objectStates snapshot → Ready for next operation
```

**📁 Files:** `lib/fabric/operations/undo.ts`, `lib/fabric/events/canvasEventListeners.ts`

```typescript
import { createUndoHandler } from "@/lib/fabric/operations/undo";

// Create undo handler with stack management
const undo = createUndoHandler(
  canvasRef,
  undoStack,
  setUndoStack,
  initialObjectCount,
  isUndoingRef
);

// Execute undo operation
await undo();

// Undo system behavior:
// - Tracks object add/remove/modify operations
// - Captures pre-modification state for rollback
// - Handles composite objects (arrows, measurements)
// - Preserves selection where possible
// - Bypasses crop operations (toggle-managed)
```

**🎯 Key Properties:**

- **State-based**: Captures complete object state, not just changes
- **Composite-aware**: Handles multi-part objects correctly
- **Selection-preserving**: Maintains selection context after undo
- **Guard-protected**: Prevents infinite loops during undo execution

---

## 🔄 Event System Architecture

### Unified Event Handler Structure

**🧠 Implementation Concept:**
The canvas event system uses a **unified pattern** for handling all annotation types, providing consistent, maintainable code. All composite objects (measurements, arrows, protractors) follow the same event handling approach.

**⚙️ Unified Event Mapping:**

```typescript
// In setupCanvasEventListeners() - Clean, consistent pattern
canvas.on("object:moving", (e) => {
  objectStateTracker(e); // Capture state for undo
  annotationUpdateHandler(e); // Update composite annotations
});

canvas.on("object:modified", (e) => {
  unifiedModifyHandler(e); // Handles all annotations + undo tracking
});
```

**✅ Unified Handler Implementation:**

```typescript
const createAnnotationUpdateHandler = (canvas: Canvas) => {
  return (e: { target: any }) => {
    if (isMeasurementLine(e.target)) {
      updateMeasurementOnModify(canvas, e.target);
    } else if (isArrow(e.target)) {
      updateArrowOnModify(canvas, e.target);
    } else if (isProtractor(e.target)) {
      onProtractorModified(canvas, e.target);
    }
  };
};
```

**🎯 Architecture Benefits:**

1. **Consistent Naming**: Clear, descriptive function names that reflect actual purpose
2. **Single Pattern**: All annotation types use the same handler structure
3. **No Redundancy**: Each handler called once per event
4. **Easy Extension**: Adding new annotation types follows established pattern

**📁 Files:** `lib/fabric/events/canvasEventListeners.ts`

---

## 🎯 Tool Modes

| Mode          | Purpose              | Selection   | Key Features                  |
| ------------- | -------------------- | ----------- | ----------------------------- |
| **Select**    | Object manipulation  | ✅ Enabled  | Move, resize, rotate objects  |
| **Freehand**  | Drawing paths        | ❌ Disabled | Hand-drawn annotations        |
| **Text**      | Text annotations     | ❌ Disabled | Readable text labels          |
| **Shapes**    | Geometric shapes     | ❌ Disabled | Rect, circle, line, highlight |
| **Arrow**     | Directional markers  | ❌ Disabled | Shaft + arrowhead             |
| **Measure**   | Distance measurement | ❌ Disabled | Calibrated measurements       |
| **Crop**      | Region selection     | ❌ Disabled | Lossless cropping             |
| **Magnifier** | Zoom inspection      | ❌ Disabled | Pixel-level magnification     |

---

## 🏗️ Development Guidelines

### Adding New Operations

1. **Create operation file** in `lib/fabric/operations/`
2. **Follow naming convention**: `operationName.ts`
3. **Implement standard pattern**:
   ```typescript
   export const applyOperationName = (canvas: Canvas, params: OperationParams) => {
     // Implementation
     return result;
   };
   ```
4. **Wire into hooks** or `useFabricViewer`
5. **Add to undo tracking** if needed

### Object Standards

All Fabric objects must follow these conventions:

- `strokeUniform: true` - Consistent stroke width on zoom
- `perPixelTargetFind: true` - Pixel-perfect selection
- `selectable: false` - Disabled by default (except Select mode)
- `evented: false` - No events by default (except Select mode)

### Selection Policy

- **Select Mode**: Objects become selectable with controls
- **Other Modes**: Selection disabled, active selection cleared
- **Text Preservation**: Flip operations preserve text readability

---

## 📁 Key File Structure

```
src/
├── lib/fabric/
│   ├── operations/          # Core operations
│   │   ├── crop.ts         # Crop functionality
│   │   ├── transforms.ts   # Rotate/flip operations
│   │   ├── filters.ts      # Image filters
│   │   ├── arrows.ts       # Arrow annotations
│   │   ├── measurements.ts # Measurement tools
│   │   ├── magnifier.ts    # Magnifier tool
│   │   ├── colorChange.ts  # Color operations
│   │   ├── undo.ts         # Undo/redo system
│   │   └── save.ts         # Save/load operations
│   ├── tools/              # Drawing tools
│   │   ├── shapeCreators.ts # Shape creation
│   │   └── toolConfigs.ts   # Tool configurations
│   ├── canvas/             # Canvas setup
│   │   └── setup.ts        # Canvas initialization
│   └── events/             # Event handling
│       └── canvasEventListeners.ts
├── hooks/
│   ├── useFabricViewer.ts  # Main viewer hook
│   ├── useResponsiveCanvas.ts # Responsive handling
│   ├── useFilterManagement.ts # Filter management
│   ├── useImageTransforms.ts  # Transform management
│   └── useCropManagement.ts   # Crop management
└── components/
    └── viewers/
        ├── ImageViewer.tsx  # Single image viewer
        └── StackViewer.tsx  # DICOM stack viewer
```

---

## ✅ Recently Fixed Issues

### Event Handler Inconsistencies - RESOLVED

**🎉 Fixed:** Unified all annotation type event handling patterns for consistent, maintainable code.

**Previous Inconsistent Pattern:**

```typescript
canvas.on("object:moving", (e) => {
  startMeasurementHandler(e); // Measurement-specific handler
  moveMeasurementHandler(e); // Measurement-specific handler
  if (isArrow(e.target)) updateArrowOnModify(canvas, e.target); // Arrow inline check
});
```

**New Unified Pattern:**

```typescript
canvas.on("object:moving", (e) => {
  objectStateTracker(e); // Capture state for undo
  annotationUpdateHandler(e); // Update composite annotations
});

// Where annotationUpdateHandler is:
const createAnnotationUpdateHandler = (canvas: Canvas) => {
  return (e: { target: any }) => {
    if (isMeasurementLine(e.target)) {
      updateMeasurementOnModify(canvas, e.target);
    } else if (isArrow(e.target)) {
      updateArrowOnModify(canvas, e.target);
    } else if (isProtractor(e.target)) {
      onProtractorModified(canvas, e.target);
    }
  };
};
```

**🔧 Improvements Made:**

- **Consistent Pattern**: All annotation types use the same handler structure
- **Clear Naming**: `objectStateTracker` instead of misleading `startMeasurementHandler`
- **No Redundancy**: Eliminated duplicate handler calls
- **Maintainable**: Easy to add new annotation types following the same pattern

**📁 Files Updated:** `lib/fabric/events/canvasEventListeners.ts`

---

## 🔧 Troubleshooting

### Common Issues

| Issue                      | Cause                 | Solution              |
| -------------------------- | --------------------- | --------------------- |
| **Objects not selectable** | Wrong tool mode       | Switch to Select mode |
| **Undo not working**       | Operation not tracked | Add to undo system    |
| **Crop not persisting**    | Missing normalization | Store normalized rect |
| **Text appears mirrored**  | Flip operation bug    | Check text flip flags |
| **Magnifier positioning**  | Transform mismatch    | Re-sync on transforms |

### Debug Tips

1. **Check selection policy** - Verify `selectable`/`evented` flags
2. **Inspect event handlers** - Look for `moving`/`scaling`/`rotating`/`modified` events
3. **Verify object properties** - Ensure `strokeUniform: true`
4. **Test transform consistency** - Check object positioning after rotate/flip

---

## 🚀 Performance Optimization

- **Object Caching**: Use `objectCaching: false` for paths only
- **Resize Optimization**: Exclude overlay objects from bulk scaling
- **Event Throttling**: Built-in ResizeObserver debouncing
- **Selection Management**: Clear active selection when switching modes

---

## 📚 API Integration

The viewer integrates with a backend API for:

- **Image Loading**: Fetch image/stack data
- **State Persistence**: Save fabricConfigs via PATCH
- **Calibration Data**: Store measurement calibrations

---
