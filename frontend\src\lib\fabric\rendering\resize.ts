import { Canvas } from "fabric";

/** Resize helpers: fit to container, aspect ratio, thresholds, and object scaling */

export const calculateFittedCanvasDimensions = (
  contentWidth: number,
  contentHeight: number,
  containerWidth: number,
  containerHeight: number
) => {
  const aspectRatio = contentWidth / contentHeight;
  let targetWidth = containerWidth;
  let targetHeight = targetWidth / aspectRatio;

  if (targetHeight > containerHeight) {
    targetHeight = containerHeight;
    targetWidth = targetHeight * aspectRatio;
  }

  return { width: targetWidth, height: targetHeight };
};

export const shouldResize = (
  currentWidth: number,
  currentHeight: number,
  targetWidth: number,
  targetHeight: number,
  threshold: number = 5
): boolean => {
  return (
    Math.abs(currentWidth - targetWidth) >= threshold ||
    Math.abs(currentHeight - targetHeight) >= threshold
  );
};

export const scaleCanvasObjects = (canvas: Canvas, imageScale: number) => {
  canvas.forEachObject((obj) => {
    const objName = (obj as any as Record<string, any>)?.name;
    if (objName !== "backgroundImage") {
      obj.set({
        scaleX: (obj.scaleX || 1) * imageScale,
        scaleY: (obj.scaleY || 1) * imageScale,
        left: (obj.left || 0) * imageScale,
        top: (obj.top || 0) * imageScale,
      });

      if ("strokeUniform" in obj) {
        obj.strokeUniform = true;
      }
      obj.setCoords();
    }
  });
};
