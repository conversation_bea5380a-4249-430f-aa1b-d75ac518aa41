import { Canvas, Rect } from "fabric";
import { CropData } from "@/shared/types";

/*
Rect orientation helpers (inline): map saved base-orientation rect to/from current
canvas orientation (arbitrary angle + flips). Works in normalized [0,1] space.
*/
export type NormalizedRect = NonNullable<CropData["normalizedCropRect"]>;

const clamp01 = (v: number) => (v < 0 ? 0 : v > 1 ? 1 : v);

const rotatePoint = (x: number, y: number, angleDeg: number) => {
  const cx = 0.5;
  const cy = 0.5;
  const theta = (angleDeg * Math.PI) / 180; // CCW
  const dx = x - cx;
  const dy = y - cy;
  const cos = Math.cos(theta);
  const sin = Math.sin(theta);
  return { x: dx * cos - dy * sin + cx, y: dx * sin + dy * cos + cy };
};

const applyFlips = (x: number, y: number, flipX: boolean, flipY: boolean) => ({
  x: flipX ? 1 - x : x,
  y: flipY ? 1 - y : y,
});

const bboxFromPoints = (pts: Array<{ x: number; y: number }>): NormalizedRect => {
  const xs = pts.map((p) => p.x);
  const ys = pts.map((p) => p.y);
  const minX = clamp01(Math.min(...xs));
  const minY = clamp01(Math.min(...ys));
  const maxX = clamp01(Math.max(...xs));
  const maxY = clamp01(Math.max(...ys));
  return { left: minX, top: minY, width: clamp01(maxX - minX), height: clamp01(maxY - minY) };
};

const toCurrentOrientation = (
  baseRect: NormalizedRect,
  angle: number,
  flipX: boolean,
  flipY: boolean
): NormalizedRect => {
  const corners = [
    { x: baseRect.left, y: baseRect.top },
    { x: baseRect.left + baseRect.width, y: baseRect.top },
    { x: baseRect.left + baseRect.width, y: baseRect.top + baseRect.height },
    { x: baseRect.left, y: baseRect.top + baseRect.height },
  ];
  const transformed = corners
    .map((p) => rotatePoint(p.x, p.y, angle))
    .map((p) => applyFlips(p.x, p.y, flipX, flipY));
  return bboxFromPoints(transformed);
};

export const toBaseOrientation = (
  currentRect: NormalizedRect,
  angle: number,
  flipX: boolean,
  flipY: boolean
): NormalizedRect => {
  const corners = [
    { x: currentRect.left, y: currentRect.top },
    { x: currentRect.left + currentRect.width, y: currentRect.top },
    { x: currentRect.left + currentRect.width, y: currentRect.top + currentRect.height },
    { x: currentRect.left, y: currentRect.top + currentRect.height },
  ];
  const unflipped = corners.map((p) => applyFlips(p.x, p.y, flipX, flipY));
  const rotatedBack = unflipped.map((p) => rotatePoint(p.x, p.y, -angle));
  return bboxFromPoints(rotatedBack);
};

/*
Apply crop: set clipPath to the drawn rect, center it, and scale with contain-fit
so the whole crop is visible. Returns the crop rect normalized to canvas size.
Used immediately when user confirms crop.
*/
export const applyCrop = (
  canvas: Canvas,
  cropRect: { left: number; top: number; width: number; height: number }
): { normalizedCropRect: CropData["normalizedCropRect"] } => {
  const { left, top, width, height } = cropRect;
  const clip = new Rect({ left, top, width, height, absolutePositioned: true });
  (canvas as any).clipPath = clip;
  // console.log("Apply Called");
  const cx = left + width / 2;
  const cy = top + height / 2;
  const vpt = canvas.viewportTransform || [1, 0, 0, 1, 0, 0];

  const canvasW = canvas.getWidth();
  const canvasH = canvas.getHeight();

  const s = Math.min(canvasW / width, canvasH / height);

  vpt[0] = s;
  vpt[3] = s;
  vpt[4] = canvasW / 2 - cx * s;
  vpt[5] = canvasH / 2 - cy * s;

  canvas.setViewportTransform(vpt as any);
  const normalizedCropRect = {
    left: left / canvasW,
    top: top / canvasH,
    width: width / canvasW,
    height: height / canvasH,
  };

  canvas.requestRenderAll();
  return { normalizedCropRect };
};

/*
Clear crop: remove clipPath and reset viewport transform to identity.
Called when user toggles crop off.
*/
export const clearCrop = (canvas: Canvas): void => {
  const vpt = canvas.viewportTransform || [1, 0, 0, 1, 0, 0];
  vpt[0] = 1;
  vpt[3] = 1;
  vpt[4] = 0;
  vpt[5] = 0;
  canvas.setViewportTransform(vpt as any);
  (canvas as any).clipPath = null;
  canvas.requestRenderAll();
};

/*
Reapply crop: take the saved base-orientation normalized rect, map it to the
current orientation (angle + flips), convert to pixels, and contain-fit center
it via viewport. Used after rotate/flip/resize so crop remains visually stable.
*/
export const applyCropFromNormalized = (
  canvas: Canvas,
  normalized: NonNullable<CropData["normalizedCropRect"]>
): void => {
  const canvasW = canvas.getWidth();
  const canvasH = canvas.getHeight();
  // console.log("Normalized Called");
  const angle = canvas.backgroundImage?.angle || 0;
  const flipX = !!canvas.backgroundImage?.flipX;
  const flipY = !!canvas.backgroundImage?.flipY;

  const mapped = toCurrentOrientation(normalized, angle, flipX, flipY);

  const left = mapped.left * canvasW;
  const top = mapped.top * canvasH;
  const width = mapped.width * canvasW;
  const height = mapped.height * canvasH;

  const clip = new Rect({ left, top, width, height, absolutePositioned: true });
  (canvas as any).clipPath = clip;

  const cx = left + width / 2;
  const cy = top + height / 2;
  const vpt = canvas.viewportTransform || [1, 0, 0, 1, 0, 0];

  const s = Math.min(canvasW / width, canvasH / height);

  vpt[0] = s;
  vpt[3] = s;
  vpt[4] = canvasW / 2 - cx * s;
  vpt[5] = canvasH / 2 - cy * s;
  canvas.setViewportTransform(vpt as any);

  canvas.requestRenderAll();
};
