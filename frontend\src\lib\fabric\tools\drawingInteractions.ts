import { Canvas, Textbox } from "fabric";
import { ToolMode, FabricMeasurementLine, CalibrationData } from "@/shared/types";
import { constrainToCanvas, transformPointer } from "./toolConfigs";
import { createInitialShape, updateShapeSize } from "./shapeCreators";
import { updateMeasurementText } from "@/lib/fabric/operations";

/*
If clicking an existing textbox, start editing it; otherwise create a new textbox
at the transformed pointer and focus it.
*/
export const handleTextToolClick = (
  pointer: { x: number; y: number },
  canvas: Canvas,
  onShapeCreated?: () => void
) => {
  const objects = canvas.getObjects();
  const clickedObject = objects.find((objectOnCanvas) => {
    if (objectOnCanvas.type === "textbox") {
      const objectBounds = objectOnCanvas.getBoundingRect();
      return (
        pointer.x >= objectBounds.left &&
        pointer.x <= objectBounds.left + objectBounds.width &&
        pointer.y >= objectBounds.top &&
        pointer.y <= objectBounds.top + objectBounds.height
      );
    }
    return false;
  });

  if (clickedObject) {
    canvas.setActiveObject(clickedObject);
    (clickedObject as Textbox).enterEditing();
    return true;
  }

  const transformedPointer = transformPointer(pointer, canvas);
  const shape = createInitialShape("text", transformedPointer, canvas);
  if (!shape) return false;

  canvas.add(shape);
  canvas.setActiveObject(shape);
  const textbox = shape as Textbox;

  textbox.enterEditing();
  textbox.selectAll();

  if (onShapeCreated) onShapeCreated();
  return true;
};

/*
Begins a drawing interaction for the current tool. Returns shape and startPoint
for continuous updates, or null if tool is not drawable at this time.
*/
export const startDrawingShape = (
  pointer: { x: number; y: number },
  activeMode: ToolMode,
  canvas: Canvas,
  isCropped: boolean,
  onShapeCreated?: () => void
) => {
  if (!activeMode || activeMode === "select") {
    return null;
  }

  if (activeMode === "crop" && isCropped) {
    return null;
  }

  const constrainedPointer = constrainToCanvas(pointer, canvas);

  if (activeMode === "text") {
    handleTextToolClick(constrainedPointer, canvas, onShapeCreated);
    return null;
  }

  const transformedPointer = transformPointer(constrainedPointer, canvas);
  const shape = createInitialShape(activeMode, transformedPointer, canvas);
  if (!shape) return null;

  if (!(shape as any).__selfAdded) {
  canvas.add(shape);
} 
  return { shape, startPoint: transformedPointer };
};

/*
Updates the shape during mouse move. For 'measure', also refreshes the overlay
text using calibration data.
*/
export const updateDrawingShape = (
  pointer: { x: number; y: number },
  activeMode: ToolMode,
  canvas: Canvas,
  currentShape: any,
  startPoint: { x: number; y: number },
  disableUndoTracking?: () => void,
  enableUndoTracking?: () => void,
  calibrationData?: CalibrationData
) => {
  if (!currentShape || !startPoint) return;

  const constrainedPointer = constrainToCanvas(pointer, canvas);
  const transformedPointer = transformPointer(constrainedPointer, canvas);

  updateShapeSize(currentShape, startPoint, transformedPointer, activeMode, canvas);

  if (activeMode === "measure" && calibrationData) {
    const line = currentShape as FabricMeasurementLine;
    if (line.measurementText) {
      disableUndoTracking?.();
      canvas.remove(line.measurementText);
      enableUndoTracking?.();
      line.measurementText = undefined;
    }
    disableUndoTracking?.();
    updateMeasurementText(canvas, line, calibrationData);
    enableUndoTracking?.();
  }
  canvas.renderAll();
};

/*
Finalizes the drawing interaction. Triggers crop or measurement text creation
as needed and notifies caller via onShapeCreated.
*/
export const finishDrawingShape = (
  activeMode: ToolMode,
  currentShape: any,
  canvas: Canvas,
  onCrop?: () => void,
  onShapeCreated?: () => void,
  disableUndoTracking?: () => void,
  enableUndoTracking?: () => void,
  calibrationData?: CalibrationData
) => {
  if (!currentShape) return;

  if (activeMode === "crop" && onCrop) onCrop();

  if (activeMode === "measure" && calibrationData) {
    const line = currentShape as FabricMeasurementLine;
    if (!line.measurementText) {
      disableUndoTracking?.();
      updateMeasurementText(canvas, line, calibrationData);
      enableUndoTracking?.();
    }
  }

  if (onShapeCreated && activeMode !== "crop") onShapeCreated();
};
